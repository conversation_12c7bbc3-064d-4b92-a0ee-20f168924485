import { Octokit } from "@octokit/rest";
import PRWalkthrough from "../../../../components/PRWalkthrough";

// Define PR data interface
interface PullRequest {
  title: string;
  html_url: string;
  state: string;
  body: string | null;
  user: {
    login: string;
    avatar_url: string;
  };
  created_at: string;
  updated_at: string;
}

export default async function Page({ params }: { params: Promise<{ org: string; repo: string; pr_num: string }> }) {
  const { org, repo, pr_num } = await params;
  
  // Create Octokit instance without auth for public repos
  const octokit = new Octokit();
  
  try {
    const response = await octokit.pulls.get({
      owner: org,
      repo: repo,
      pull_number: parseInt(pr_num),
    });
    
    const pr: PullRequest = response.data;
    
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4">{pr.title}</h1>
        
        <div className="flex items-center mb-4">
          <img src={pr.user.avatar_url} alt={pr.user.login} className="w-10 h-10 rounded-full mr-3" />
          <div>
            <p><strong>{pr.user.login}</strong> opened this PR on {new Date(pr.created_at).toLocaleDateString()}</p>
            <p>Status: <span className={`font-semibold ${pr.state === 'open' ? 'text-green-600' : 'text-purple-600'}`}>
              {pr.state}
            </span></p>
          </div>
        </div>
        
        {/* PR Description */}
        <div className="border rounded-md p-4 mb-6 bg-gray-50 dark:bg-gray-800">
          <h2 className="text-lg font-semibold mb-2">Description</h2>
          <div className="whitespace-pre-wrap">{pr.body || "No description provided."}</div>
        </div>
        
        <a
          href={pr.html_url}
          className="inline-block px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition mb-6"
        >
          View on GitHub: {org}/{repo}#{pr_num}
        </a>

        {/* PR Walkthrough Section */}
        <PRWalkthrough org={org} repo={repo} pr_num={pr_num} />
      </div>
    );
  } catch (error) {
    console.error("Error fetching PR:", error);
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <p className="text-red-500 mb-4">Error fetching PR information</p>
        <a
          href={`https://github.com/${org}/${repo}/pull/${pr_num}`}
          className="inline-block px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition mb-6"
        >
          View on GitHub: {org}/{repo}#{pr_num}
        </a>

        {/* PR Walkthrough Section - Still available even if PR fetch failed */}
        <PRWalkthrough org={org} repo={repo} pr_num={pr_num} />
      </div>
    );
  }
}
