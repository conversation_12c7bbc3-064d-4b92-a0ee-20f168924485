import { NextRequest, NextResponse } from 'next/server';
import { fetchPullRequestData, formatChangesForAnalysis } from '../../../utils/githubUtils';
import { analyzePullRequest } from '../../../services/aiAnalysis';
import { PRAnalysisResult } from '../../../types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { org, repo, pr_num } = body;

    // Validate required parameters
    if (!org || !repo || !pr_num) {
      return NextResponse.json(
        { error: 'Missing required parameters: org, repo, pr_num' },
        { status: 400 }
      );
    }

    const prNumber = parseInt(pr_num);
    if (isNaN(prNumber)) {
      return NextResponse.json(
        { error: 'Invalid PR number' },
        { status: 400 }
      );
    }

    // Step 1: Fetch PR data and diffs
    let prData;
    try {
      prData = await fetchPullRequestData(org, repo, prNumber);
    } catch (error) {
      console.error('Error fetching PR data:', error);
      return NextResponse.json(
        { error: 'Failed to fetch PR data from GitHub' },
        { status: 500 }
      );
    }

    // Step 2: Format changes for AI analysis
    const changesText = formatChangesForAnalysis(prData);

    // Step 3: Run AI analysis
    let analysisResult: PRAnalysisResult;
    try {
      analysisResult = await analyzePullRequest(prData, changesText);
    } catch (error) {
      console.error('Error in AI analysis:', error);
      return NextResponse.json(
        { error: 'Failed to analyze PR with AI' },
        { status: 500 }
      );
    }

    // Return the complete analysis
    return NextResponse.json({
      success: true,
      data: analysisResult,
      prData: {
        title: prData.title,
        html_url: prData.html_url,
        state: prData.state,
        user: prData.user,
        created_at: prData.created_at,
        updated_at: prData.updated_at,
        additions: prData.additions,
        deletions: prData.deletions,
        changed_files: prData.changed_files,
      }
    });

  } catch (error) {
    console.error('Unexpected error in PR analysis:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint for checking analysis status or retrieving cached results
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const org = searchParams.get('org');
  const repo = searchParams.get('repo');
  const pr_num = searchParams.get('pr_num');

  if (!org || !repo || !pr_num) {
    return NextResponse.json(
      { error: 'Missing required parameters: org, repo, pr_num' },
      { status: 400 }
    );
  }

  // For now, just return that no cached analysis exists
  // In the future, you could implement caching with Redis or a database
  return NextResponse.json({
    success: false,
    message: 'No cached analysis found',
  });
}
