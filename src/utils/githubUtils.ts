import { ParsedPrUrl } from '../types';

export function parseGitHubPrUrl(url: string): ParsedPrUrl | null {
  try {
    // Remove any trailing whitespace and normalize the URL
    const cleanUrl = url.trim();
    
    // Match GitHub PR URL pattern
    const regex = /^https?:\/\/github\.com\/([^\/]+)\/([^\/]+)\/pull\/(\d+)(?:\/.*)?$/;
    const match = cleanUrl.match(regex);
    
    if (!match) {
      return null;
    }
    
    const [, org, repo, pr_num] = match;
    
    return {
      org,
      repo,
      pr_num
    };
  } catch {
    return null;
  }
}
