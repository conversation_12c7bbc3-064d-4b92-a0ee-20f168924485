import { Octokit } from "@octokit/rest";
import { ParsedPrUrl, PullRequestData, GitHubFile } from '../types';

export function parseGitHubPrUrl(url: string): ParsedPrUrl | null {
  try {
    // Remove any trailing whitespace and normalize the URL
    const cleanUrl = url.trim();

    // Match GitHub PR URL pattern
    const regex = /^https?:\/\/github\.com\/([^\/]+)\/([^\/]+)\/pull\/(\d+)(?:\/.*)?$/;
    const match = cleanUrl.match(regex);

    if (!match) {
      return null;
    }

    const [, org, repo, pr_num] = match;

    return {
      org,
      repo,
      pr_num
    };
  } catch {
    return null;
  }
}

/**
 * Create an authenticated Octokit instance
 */
export function createOctokit(): Octokit {
  const token = process.env.GITHUB_TOKEN;
  return new Octokit({
    auth: token,
  });
}

/**
 * Fetch PR data including files and diffs
 */
export async function fetchPullRequestData(
  org: string,
  repo: string,
  prNumber: number
): Promise<PullRequestData> {
  const octokit = createOctokit();

  try {
    // Fetch PR details
    const prResponse = await octokit.pulls.get({
      owner: org,
      repo: repo,
      pull_number: prNumber,
    });

    // Fetch PR files with diffs
    const filesResponse = await octokit.pulls.listFiles({
      owner: org,
      repo: repo,
      pull_number: prNumber,
    });

    const pr = prResponse.data;
    const files: GitHubFile[] = filesResponse.data.map(file => ({
      filename: file.filename,
      status: file.status as GitHubFile['status'],
      additions: file.additions,
      deletions: file.deletions,
      changes: file.changes,
      patch: file.patch,
      previous_filename: file.previous_filename,
    }));

    return {
      title: pr.title,
      body: pr.body,
      html_url: pr.html_url,
      state: pr.state,
      user: {
        login: pr.user?.login || 'unknown',
        avatar_url: pr.user?.avatar_url || '',
      },
      created_at: pr.created_at,
      updated_at: pr.updated_at,
      additions: pr.additions || 0,
      deletions: pr.deletions || 0,
      changed_files: pr.changed_files || 0,
      files,
    };
  } catch (error) {
    console.error('Error fetching PR data:', error);
    throw new Error(`Failed to fetch PR data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get a summary of changes for AI analysis
 */
export function formatChangesForAnalysis(prData: PullRequestData): string {
  const { title, body, files, additions, deletions } = prData;

  let summary = `PR Title: ${title}\n\n`;

  if (body) {
    summary += `PR Description:\n${body}\n\n`;
  }

  summary += `Changes Summary:\n`;
  summary += `- ${files.length} files changed\n`;
  summary += `- ${additions} additions\n`;
  summary += `- ${deletions} deletions\n\n`;

  summary += `Files Changed:\n`;
  files.forEach(file => {
    summary += `\n${file.status.toUpperCase()}: ${file.filename}\n`;
    summary += `  +${file.additions} -${file.deletions}\n`;

    if (file.patch && file.patch.length < 2000) { // Limit patch size
      summary += `  Diff:\n${file.patch}\n`;
    } else if (file.patch) {
      summary += `  Diff: [Large diff truncated - ${file.patch.length} characters]\n`;
    }
  });

  return summary;
}
