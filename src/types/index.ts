export interface ParsedPrUrl {
  org: string;
  repo: string;
  pr_num: string;
}

// GitHub API types
export interface GitHubFile {
  filename: string;
  status: 'added' | 'removed' | 'modified' | 'renamed';
  additions: number;
  deletions: number;
  changes: number;
  patch?: string;
  previous_filename?: string;
}

export interface PullRequestData {
  title: string;
  body: string | null;
  html_url: string;
  state: string;
  user: {
    login: string;
    avatar_url: string;
  };
  created_at: string;
  updated_at: string;
  additions: number;
  deletions: number;
  changed_files: number;
  files: GitHubFile[];
}

// PR Walkthrough Analysis types
export interface UseCase {
  type: 'bug_fix' | 'new_feature' | 'refactor' | 'maintenance' | 'unclear';
  description: string;
  concreteExample?: string;
  beforeAfter?: {
    before: string;
    after: string;
  };
}

export interface ChangeGroup {
  id: string;
  title: string;
  description: string;
  files: string[];
  changeType: 'core_logic' | 'configuration' | 'tests' | 'documentation' | 'mechanical' | 'setup';
  importance: 'critical' | 'important' | 'supporting' | 'minor';
  diffHunks: string[];
  explanation: string;
  reviewNotes?: string[];
}

export interface PRWalkthroughResult {
  useCase: UseCase;
  summary: string;
  changeGroups: ChangeGroup[];
  reviewOrder: string[]; // Array of change group IDs in recommended review order
  analysisTimestamp: string;
}

export interface AnalysisProgress {
  stage: 'fetching' | 'analyzing' | 'complete' | 'error';
  message: string;
  progress: number; // 0-100
}
