export interface ParsedPrUrl {
  org: string;
  repo: string;
  pr_num: string;
}

// GitHub API types
export interface GitHubFile {
  filename: string;
  status: 'added' | 'removed' | 'modified' | 'renamed';
  additions: number;
  deletions: number;
  changes: number;
  patch?: string;
  previous_filename?: string;
}

export interface PullRequestData {
  title: string;
  body: string | null;
  html_url: string;
  state: string;
  user: {
    login: string;
    avatar_url: string;
  };
  created_at: string;
  updated_at: string;
  additions: number;
  deletions: number;
  changed_files: number;
  files: GitHubFile[];
}

// AI Analysis types
export interface CodeQualityAnalysis {
  score: number; // 1-10
  issues: string[];
  suggestions: string[];
  complexity: 'low' | 'medium' | 'high';
}

export interface SecurityAnalysis {
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  vulnerabilities: string[];
  recommendations: string[];
}

export interface PerformanceAnalysis {
  impact: 'positive' | 'negative' | 'neutral';
  concerns: string[];
  optimizations: string[];
}

export interface TestingAnalysis {
  coverage: 'good' | 'partial' | 'missing';
  testQuality: string[];
  missingTests: string[];
}

export interface DocumentationAnalysis {
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  missingDocs: string[];
  suggestions: string[];
}

export interface PRAnalysisResult {
  summary: string;
  overallScore: number; // 1-10
  codeQuality: CodeQualityAnalysis;
  security: SecurityAnalysis;
  performance: PerformanceAnalysis;
  testing: TestingAnalysis;
  documentation: DocumentationAnalysis;
  keyChanges: string[];
  recommendations: string[];
  analysisTimestamp: string;
}

export interface AnalysisProgress {
  stage: 'fetching' | 'analyzing' | 'complete' | 'error';
  message: string;
  progress: number; // 0-100
}
