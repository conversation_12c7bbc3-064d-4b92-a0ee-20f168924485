interface AnalysisCardProps {
  title: string;
  score?: number;
  status?: string;
  statusColor?: string;
  children: React.ReactNode;
  className?: string;
}

export default function AnalysisCard({ 
  title, 
  score, 
  status, 
  statusColor = 'text-gray-600',
  children, 
  className = '' 
}: AnalysisCardProps) {
  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className={`border rounded-md p-4 bg-white dark:bg-gray-800 ${className}`}>
      <h4 className="font-medium mb-2 flex items-center justify-between">
        <span>{title}</span>
        <div className="flex items-center space-x-2">
          {score !== undefined && (
            <span className={`font-bold ${getScoreColor(score)}`}>
              {score}/10
            </span>
          )}
          {status && (
            <span className={`text-sm font-medium ${statusColor}`}>
              {status}
            </span>
          )}
        </div>
      </h4>
      {children}
    </div>
  );
}
