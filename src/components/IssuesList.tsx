interface IssuesListProps {
  title: string;
  items: string[];
  type: 'error' | 'warning' | 'info' | 'success';
  emptyMessage?: string;
}

export default function IssuesList({ 
  title, 
  items, 
  type, 
  emptyMessage = 'No items found' 
}: IssuesListProps) {
  const getTypeStyles = (type: string) => {
    switch (type) {
      case 'error':
        return {
          titleColor: 'text-red-600',
          iconColor: 'text-red-500',
          icon: '⚠️'
        };
      case 'warning':
        return {
          titleColor: 'text-orange-600',
          iconColor: 'text-orange-500',
          icon: '⚠️'
        };
      case 'info':
        return {
          titleColor: 'text-blue-600',
          iconColor: 'text-blue-500',
          icon: 'ℹ️'
        };
      case 'success':
        return {
          titleColor: 'text-green-600',
          iconColor: 'text-green-500',
          icon: '✅'
        };
      default:
        return {
          titleColor: 'text-gray-600',
          iconColor: 'text-gray-500',
          icon: '•'
        };
    }
  };

  const styles = getTypeStyles(type);

  if (items.length === 0) {
    return (
      <div className="text-sm text-gray-500 italic">
        {emptyMessage}
      </div>
    );
  }

  return (
    <div>
      <p className={`text-sm font-medium ${styles.titleColor} mb-1`}>
        {title}:
      </p>
      <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
        {items.map((item, index) => (
          <li key={index} className="flex items-start">
            <span className={`mr-2 ${styles.iconColor}`}>
              {styles.icon}
            </span>
            <span>{item}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
