'use client';

import { useState } from 'react';
import { PRWalkthroughResult, AnalysisProgress } from '../types';
import AnalysisCard from './AnalysisCard';
import ProgressBar from './ProgressBar';

interface PRWalkthroughProps {
  org: string;
  repo: string;
  pr_num: string;
}

export default function PRWalkthrough({ org, repo, pr_num }: PRWalkthroughProps) {
  const [walkthrough, setWalkthrough] = useState<PRWalkthroughResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<AnalysisProgress>({
    stage: 'fetching',
    message: 'Ready to analyze',
    progress: 0,
  });

  const startAnalysis = async () => {
    setLoading(true);
    setError(null);
    setWalkthrough(null);
    
    try {
      setProgress({
        stage: 'fetching',
        message: 'Fetching PR data from GitHub...',
        progress: 20,
      });

      const response = await fetch('/api/analyze-pr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ org, repo, pr_num }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Analysis failed');
      }

      setProgress({
        stage: 'analyzing',
        message: 'Creating walkthrough...',
        progress: 60,
      });

      const result = await response.json();
      
      setProgress({
        stage: 'complete',
        message: 'Walkthrough complete!',
        progress: 100,
      });

      setWalkthrough(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setProgress({
        stage: 'error',
        message: 'Analysis failed',
        progress: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getImportanceIcon = (importance: string) => {
    switch (importance) {
      case 'critical': return '🔴';
      case 'important': return '🟡';
      case 'supporting': return '🔵';
      case 'minor': return '⚪';
      default: return '⚫';
    }
  };

  const getChangeTypeIcon = (changeType: string) => {
    switch (changeType) {
      case 'core_logic': return '⚙️';
      case 'configuration': return '🔧';
      case 'tests': return '🧪';
      case 'documentation': return '📝';
      case 'mechanical': return '🔄';
      case 'setup': return '🏗️';
      default: return '📄';
    }
  };

  return (
    <div className="border rounded-lg p-6 bg-white dark:bg-gray-800">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">PR Walkthrough</h2>
        <button
          onClick={startAnalysis}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Creating Walkthrough...' : 'Start Walkthrough'}
        </button>
      </div>

      {loading && (
        <ProgressBar 
          progress={progress.progress}
          message={progress.message}
          stage={progress.stage}
        />
      )}

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-700">Error: {error}</p>
        </div>
      )}

      {walkthrough && (
        <div className="space-y-6">
          {/* Use Case Section */}
          <AnalysisCard title="What's the Use Case?" className="border-l-4 border-l-blue-500">
            <div className="space-y-3">
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-600 mr-2">Type:</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">
                  {walkthrough.useCase.type.replace('_', ' ')}
                </span>
              </div>
              
              <p className="text-gray-700 dark:text-gray-300">
                {walkthrough.useCase.description}
              </p>

              {walkthrough.useCase.concreteExample && (
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Concrete Example:
                  </p>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {walkthrough.useCase.concreteExample}
                  </p>
                </div>
              )}

              {walkthrough.useCase.beforeAfter && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
                    <p className="text-sm font-medium text-red-700 dark:text-red-400 mb-1">
                      Before:
                    </p>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {walkthrough.useCase.beforeAfter.before}
                    </p>
                  </div>
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md">
                    <p className="text-sm font-medium text-green-700 dark:text-green-400 mb-1">
                      After:
                    </p>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {walkthrough.useCase.beforeAfter.after}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </AnalysisCard>

          {/* Summary */}
          <div className="border-b pb-4">
            <h3 className="text-lg font-medium mb-2">Summary</h3>
            <p className="text-gray-700 dark:text-gray-300">{walkthrough.summary}</p>
          </div>

          {/* Change Groups */}
          <div>
            <h3 className="text-lg font-medium mb-4">Changes Breakdown</h3>
            <div className="space-y-4">
              {walkthrough.reviewOrder.map((groupId, index) => {
                const group = walkthrough.changeGroups.find(g => g.id === groupId);
                if (!group) return null;

                return (
                  <AnalysisCard 
                    key={group.id}
                    title={`${index + 1}. ${group.title}`}
                    className="border-l-4 border-l-gray-300"
                  >
                    <div className="space-y-3">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <span className="mr-1">{getImportanceIcon(group.importance)}</span>
                          <span className="text-sm text-gray-600">{group.importance}</span>
                        </div>
                        <div className="flex items-center">
                          <span className="mr-1">{getChangeTypeIcon(group.changeType)}</span>
                          <span className="text-sm text-gray-600">{group.changeType.replace('_', ' ')}</span>
                        </div>
                      </div>

                      <p className="text-gray-700 dark:text-gray-300">
                        {group.description}
                      </p>

                      <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Explanation:
                        </p>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {group.explanation}
                        </p>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Files ({group.files.length}):
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {group.files.map((file, fileIndex) => (
                            <span 
                              key={fileIndex}
                              className="px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-xs"
                            >
                              {file}
                            </span>
                          ))}
                        </div>
                      </div>

                      {group.reviewNotes && group.reviewNotes.length > 0 && (
                        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md">
                          <p className="text-sm font-medium text-yellow-700 dark:text-yellow-400 mb-1">
                            Review Notes:
                          </p>
                          <ul className="text-sm text-gray-700 dark:text-gray-300 list-disc list-inside">
                            {group.reviewNotes.map((note, noteIndex) => (
                              <li key={noteIndex}>{note}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </AnalysisCard>
                );
              })}
            </div>
          </div>

          {/* Overall Assessment */}
          <AnalysisCard title="Overall Assessment" className="border-l-4 border-l-green-500">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">Complexity</p>
                <p className={`font-bold ${getComplexityColor(walkthrough.overallAssessment.complexity)}`}>
                  {walkthrough.overallAssessment.complexity}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Risk Level</p>
                <p className={`font-bold ${getRiskColor(walkthrough.overallAssessment.riskLevel)}`}>
                  {walkthrough.overallAssessment.riskLevel}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Readiness Score</p>
                <p className="font-bold text-blue-600">
                  {walkthrough.overallAssessment.readinessScore}/10
                </p>
              </div>
            </div>

            {walkthrough.overallAssessment.keyReviewPoints.length > 0 && (
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  Key Review Points:
                </p>
                <ul className="list-disc list-inside space-y-1">
                  {walkthrough.overallAssessment.keyReviewPoints.map((point, index) => (
                    <li key={index} className="text-sm text-gray-700 dark:text-gray-300">
                      {point}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </AnalysisCard>

          <div className="text-xs text-gray-500 border-t pt-2">
            Walkthrough created at: {new Date(walkthrough.analysisTimestamp).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  );
}
