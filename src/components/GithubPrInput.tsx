'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { parseGitHubPrUrl } from '../utils/githubUtils';
import { ParsedPrUrl } from '../types';
import { ArrowRightIcon, AlertTriangleIcon } from './Icons';

interface GithubPrInputProps {
  onSubmit?: (prData: ParsedPrUrl) => void;
}

const GithubPrInput: React.FC<GithubPrInputProps> = ({ onSubmit }) => {
  const [prUrl, setPrUrl] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = useCallback((e?: React.FormEvent<HTMLFormElement>) => {
    if (e) e.preventDefault();
    if (isLoading) return;

    // Don't proceed if URL is empty
    if (!prUrl.trim()) {
      setError('Please enter a GitHub PR URL');
      return;
    }

    setError(null);
    setIsLoading(true);

    // Parse the URL immediately
    const parsed = parseGitHubPrUrl(prUrl.trim());

    if (parsed) {
      if (onSubmit) {
        onSubmit(parsed);
        setIsLoading(false); // Reset loading state after callback
      } else {
        // Navigate using Next.js router
        router.push(`/gh/${parsed.org}/${parsed.repo}/${parsed.pr_num}`);
        // Don't reset loading state immediately - let the navigation happen
      }
    } else {
      setError('Invalid GitHub PR URL. Example: https://github.com/owner/repo/pull/123');
      setIsLoading(false);
    }

  }, [prUrl, onSubmit, router, isLoading]);

  return (
    <form onSubmit={handleSubmit} className="space-y-4 w-full">
      <div className="relative flex items-center group">
        <input
          type="text"
          value={prUrl}
          onChange={(e) => {
            setPrUrl(e.target.value);
            if (error) setError(null);
          }}
          placeholder="Paste GitHub PR URL..."
          className="w-full pl-5 pr-28 sm:pr-32 py-4 text-base sm:text-lg bg-slate-700/80 border-2 border-slate-600 rounded-xl shadow-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:outline-none transition-all duration-200 placeholder-slate-400 text-slate-100 backdrop-blur-sm"
          aria-label="GitHub PR URL"
          disabled={isLoading}
        />
        <button
          type="submit"
          className={`absolute right-2 top-1/2 -translate-y-1/2 font-semibold py-2.5 px-4 sm:px-6 rounded-lg transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-slate-800 flex items-center justify-center shadow-md ${
            isLoading
              ? 'bg-slate-500 cursor-not-allowed'
              : 'bg-purple-600 hover:bg-purple-700 text-white'
          }`}
          aria-label="Analyze PR"
          disabled={isLoading}
        >
          {isLoading ? (
            <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <>
              <span className="hidden sm:inline">Glide</span>
              <ArrowRightIcon className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
            </>
          )}
        </button>
      </div>
      {error && (
        <div className="flex items-center text-red-400 bg-red-900/40 p-3 rounded-lg border border-red-700/60 shadow-md animate-shake">
          <AlertTriangleIcon className="w-5 h-5 mr-2.5 flex-shrink-0" />
          <p className="text-sm font-medium">{error}</p>
        </div>
      )}
      <style jsx>{`
        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
          20%, 40%, 60%, 80% { transform: translateX(3px); }
        }
        .animate-shake {
          animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        }
      `}</style>
    </form>
  );
};

export default GithubPrInput;
