'use client';

import { useState } from 'react';
import { PRWalkthroughResult, AnalysisProgress } from '../types';
import AnalysisCard from './AnalysisCard';
import ProgressBar from './ProgressBar';

interface PRAnalysisProps {
  org: string;
  repo: string;
  pr_num: string;
}

export default function PRAnalysis({ org, repo, pr_num }: PRAnalysisProps) {
  const [analysis, setAnalysis] = useState<PRWalkthroughResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<AnalysisProgress>({
    stage: 'fetching',
    message: 'Ready to analyze',
    progress: 0,
  });

  const startAnalysis = async () => {
    setLoading(true);
    setError(null);
    setAnalysis(null);
    
    try {
      setProgress({
        stage: 'fetching',
        message: 'Fetching PR data from GitHub...',
        progress: 20,
      });

      const response = await fetch('/api/analyze-pr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ org, repo, pr_num }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Analysis failed');
      }

      setProgress({
        stage: 'analyzing',
        message: 'Running AI analysis...',
        progress: 60,
      });

      const result = await response.json();
      
      setProgress({
        stage: 'complete',
        message: 'Analysis complete!',
        progress: 100,
      });

      setAnalysis(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setProgress({
        stage: 'error',
        message: 'Analysis failed',
        progress: 0,
      });
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="border rounded-lg p-6 bg-white dark:bg-gray-800">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">AI Analysis</h2>
        <button
          onClick={startAnalysis}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Analyzing...' : 'Start Analysis'}
        </button>
      </div>

      {loading && (
        <ProgressBar
          progress={progress.progress}
          message={progress.message}
          stage={progress.stage}
        />
      )}

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-700">Error: {error}</p>
        </div>
      )}

      {analysis && (
        <div className="space-y-6">
          {/* Summary */}
          <div className="border-b pb-4">
            <h3 className="text-lg font-medium mb-2">Summary</h3>
            <p className="text-gray-700 dark:text-gray-300">{analysis.summary}</p>
          </div>

          {/* Use Case */}
          <div>
            <h3 className="text-lg font-medium mb-2">Use Case</h3>
            <p className="text-gray-700 dark:text-gray-300">{analysis.useCase.description}</p>
          </div>

          {/* Change Groups Overview */}
          <AnalysisCard title="Change Groups">
            <p className="text-gray-700 dark:text-gray-300">
              This PR contains <span className="font-bold text-blue-600">{analysis.changeGroups.length}</span> logical groups of changes.
            </p>
          </AnalysisCard>

          <div className="text-xs text-gray-500 border-t pt-2">
            Analysis completed at: {new Date(analysis.analysisTimestamp).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  );
}
