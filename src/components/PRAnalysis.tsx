'use client';

import { useState } from 'react';
import { PRWalkthroughResult, AnalysisProgress } from '../types';
import AnalysisCard from './AnalysisCard';
import IssuesList from './IssuesList';
import ProgressBar from './ProgressBar';

interface PRAnalysisProps {
  org: string;
  repo: string;
  pr_num: string;
}

export default function PRAnalysis({ org, repo, pr_num }: PRAnalysisProps) {
  const [analysis, setAnalysis] = useState<PRWalkthroughResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<AnalysisProgress>({
    stage: 'fetching',
    message: 'Ready to analyze',
    progress: 0,
  });

  const startAnalysis = async () => {
    setLoading(true);
    setError(null);
    setAnalysis(null);
    
    try {
      setProgress({
        stage: 'fetching',
        message: 'Fetching PR data from GitHub...',
        progress: 20,
      });

      const response = await fetch('/api/analyze-pr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ org, repo, pr_num }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Analysis failed');
      }

      setProgress({
        stage: 'analyzing',
        message: 'Running AI analysis...',
        progress: 60,
      });

      const result = await response.json();
      
      setProgress({
        stage: 'complete',
        message: 'Analysis complete!',
        progress: 100,
      });

      setAnalysis(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setProgress({
        stage: 'error',
        message: 'Analysis failed',
        progress: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-orange-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="border rounded-lg p-6 bg-white dark:bg-gray-800">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">AI Analysis</h2>
        <button
          onClick={startAnalysis}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Analyzing...' : 'Start Analysis'}
        </button>
      </div>

      {loading && (
        <ProgressBar
          progress={progress.progress}
          message={progress.message}
          stage={progress.stage}
        />
      )}

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-700">Error: {error}</p>
        </div>
      )}

      {analysis && (
        <div className="space-y-6">
          {/* Overall Summary */}
          <div className="border-b pb-4">
            <h3 className="text-lg font-medium mb-2">Summary</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-2">{analysis.summary}</p>
            <div className="flex items-center">
              <span className="text-sm font-medium mr-2">Overall Score:</span>
              <span className={`text-lg font-bold ${getScoreColor(analysis.overallScore)}`}>
                {analysis.overallScore}/10
              </span>
            </div>
          </div>

          {/* Key Changes */}
          <div>
            <h3 className="text-lg font-medium mb-2">Key Changes</h3>
            <ul className="list-disc list-inside space-y-1">
              {analysis.keyChanges.map((change, index) => (
                <li key={index} className="text-gray-700 dark:text-gray-300">{change}</li>
              ))}
            </ul>
          </div>

          {/* Analysis Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Code Quality */}
            <AnalysisCard
              title="Code Quality"
              score={analysis.codeQuality.score}
            >
              <p className="text-sm text-gray-600 mb-2">
                Complexity: <span className="font-medium">{analysis.codeQuality.complexity}</span>
              </p>
              <IssuesList
                title="Issues"
                items={analysis.codeQuality.issues}
                type="error"
                emptyMessage="No code quality issues found"
              />
              {analysis.codeQuality.suggestions.length > 0 && (
                <div className="mt-2">
                  <IssuesList
                    title="Suggestions"
                    items={analysis.codeQuality.suggestions}
                    type="info"
                  />
                </div>
              )}
            </AnalysisCard>

            {/* Security */}
            <AnalysisCard
              title="Security"
              status={analysis.security.riskLevel}
              statusColor={getRiskColor(analysis.security.riskLevel)}
            >
              <IssuesList
                title="Vulnerabilities"
                items={analysis.security.vulnerabilities}
                type="error"
                emptyMessage="No security vulnerabilities found"
              />
              {analysis.security.recommendations.length > 0 && (
                <div className="mt-2">
                  <IssuesList
                    title="Security Recommendations"
                    items={analysis.security.recommendations}
                    type="warning"
                  />
                </div>
              )}
            </AnalysisCard>

            {/* Performance */}
            <AnalysisCard
              title="Performance"
              status={`${analysis.performance.impact} impact`}
              statusColor={
                analysis.performance.impact === 'positive' ? 'text-green-600' :
                analysis.performance.impact === 'negative' ? 'text-red-600' : 'text-gray-600'
              }
            >
              <IssuesList
                title="Performance Concerns"
                items={analysis.performance.concerns}
                type="warning"
                emptyMessage="No performance concerns identified"
              />
              {analysis.performance.optimizations.length > 0 && (
                <div className="mt-2">
                  <IssuesList
                    title="Optimization Opportunities"
                    items={analysis.performance.optimizations}
                    type="success"
                  />
                </div>
              )}
            </AnalysisCard>

            {/* Testing */}
            <AnalysisCard
              title="Testing"
              status={`${analysis.testing.coverage} coverage`}
              statusColor={
                analysis.testing.coverage === 'good' ? 'text-green-600' :
                analysis.testing.coverage === 'partial' ? 'text-yellow-600' : 'text-red-600'
              }
            >
              <IssuesList
                title="Missing Tests"
                items={analysis.testing.missingTests}
                type="error"
                emptyMessage="Test coverage appears adequate"
              />
              {analysis.testing.testQuality.length > 0 && (
                <div className="mt-2">
                  <IssuesList
                    title="Test Quality Notes"
                    items={analysis.testing.testQuality}
                    type="info"
                  />
                </div>
              )}
            </AnalysisCard>
          </div>

          {/* Recommendations */}
          <div>
            <h3 className="text-lg font-medium mb-2">Recommendations</h3>
            <ul className="list-disc list-inside space-y-1">
              {analysis.recommendations.map((rec, index) => (
                <li key={index} className="text-gray-700 dark:text-gray-300">{rec}</li>
              ))}
            </ul>
          </div>

          <div className="text-xs text-gray-500 border-t pt-2">
            Analysis completed at: {new Date(analysis.analysisTimestamp).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  );
}
