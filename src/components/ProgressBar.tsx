interface ProgressBarProps {
  progress: number; // 0-100
  message: string;
  stage: 'fetching' | 'analyzing' | 'complete' | 'error';
  className?: string;
}

export default function ProgressBar({ 
  progress, 
  message, 
  stage, 
  className = '' 
}: ProgressBarProps) {
  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'fetching':
        return 'bg-blue-600';
      case 'analyzing':
        return 'bg-purple-600';
      case 'complete':
        return 'bg-green-600';
      case 'error':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'fetching':
        return '📥';
      case 'analyzing':
        return '🤖';
      case 'complete':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '⏳';
    }
  };

  return (
    <div className={`mb-4 ${className}`}>
      <div className="flex items-center mb-2">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStageColor(stage)}`}
            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          ></div>
        </div>
        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400 min-w-[3rem]">
          {progress}%
        </span>
      </div>
      <div className="flex items-center">
        <span className="mr-2">{getStageIcon(stage)}</span>
        <p className="text-sm text-gray-600 dark:text-gray-400">{message}</p>
      </div>
    </div>
  );
}
