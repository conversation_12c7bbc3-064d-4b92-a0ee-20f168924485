import { generateObject } from 'ai';
import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { z } from 'zod';
import { PRAnalysisResult, PullRequestData } from '../types';

// Zod schemas for structured AI responses
const CodeQualitySchema = z.object({
  score: z.number().min(1).max(10),
  issues: z.array(z.string()),
  suggestions: z.array(z.string()),
  complexity: z.enum(['low', 'medium', 'high']),
});

const SecuritySchema = z.object({
  riskLevel: z.enum(['low', 'medium', 'high', 'critical']),
  vulnerabilities: z.array(z.string()),
  recommendations: z.array(z.string()),
});

const PerformanceSchema = z.object({
  impact: z.enum(['positive', 'negative', 'neutral']),
  concerns: z.array(z.string()),
  optimizations: z.array(z.string()),
});

const TestingSchema = z.object({
  coverage: z.enum(['good', 'partial', 'missing']),
  testQuality: z.array(z.string()),
  missingTests: z.array(z.string()),
});

const DocumentationSchema = z.object({
  quality: z.enum(['excellent', 'good', 'fair', 'poor']),
  missingDocs: z.array(z.string()),
  suggestions: z.array(z.string()),
});

const PRAnalysisSchema = z.object({
  summary: z.string(),
  overallScore: z.number().min(1).max(10),
  codeQuality: CodeQualitySchema,
  security: SecuritySchema,
  performance: PerformanceSchema,
  testing: TestingSchema,
  documentation: DocumentationSchema,
  keyChanges: z.array(z.string()),
  recommendations: z.array(z.string()),
});

/**
 * Get the AI model based on environment configuration
 */
function getAIModel() {
  if (process.env.OPENAI_API_KEY) {
    return openai('gpt-4o');
  } else if (process.env.ANTHROPIC_API_KEY) {
    return anthropic('claude-3-5-sonnet-20241022');
  } else {
    throw new Error('No AI provider configured. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY');
  }
}

/**
 * Generate comprehensive PR analysis using AI
 */
export async function analyzePullRequest(
  prData: PullRequestData,
  changesText: string
): Promise<PRAnalysisResult> {
  const model = getAIModel();
  
  const systemPrompt = `You are an expert code reviewer and software engineer. Analyze the provided pull request and provide a comprehensive review covering code quality, security, performance, testing, and documentation.

Be thorough but concise. Focus on actionable insights and specific recommendations. Consider the context of the changes and their potential impact.

For scoring, use this scale:
- 1-3: Poor (major issues, significant problems)
- 4-6: Fair (some issues, room for improvement)
- 7-8: Good (minor issues, mostly solid)
- 9-10: Excellent (high quality, best practices followed)`;

  const userPrompt = `Please analyze this pull request:

${changesText}

Provide a comprehensive analysis covering:
1. Code quality and maintainability
2. Security implications
3. Performance impact
4. Testing coverage and quality
5. Documentation completeness
6. Overall assessment and recommendations

Focus on being specific and actionable in your feedback.`;

  try {
    const result = await generateObject({
      model,
      system: systemPrompt,
      prompt: userPrompt,
      schema: PRAnalysisSchema,
      temperature: 0.3, // Lower temperature for more consistent analysis
    });

    return {
      ...result.object,
      analysisTimestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Error in AI analysis:', error);
    throw new Error(`AI analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate a quick summary for immediate feedback
 */
export async function generateQuickSummary(prData: PullRequestData): Promise<string> {
  const model = getAIModel();
  
  const prompt = `Provide a brief 2-3 sentence summary of this pull request:

Title: ${prData.title}
Description: ${prData.body || 'No description provided'}
Files changed: ${prData.changed_files}
Additions: ${prData.additions}
Deletions: ${prData.deletions}

Focus on what the PR accomplishes and its scope.`;

  try {
    const { text } = await generateObject({
      model,
      prompt,
      schema: z.object({
        summary: z.string(),
      }),
    });

    return text;
  } catch (error) {
    console.error('Error generating quick summary:', error);
    return `This PR modifies ${prData.changed_files} files with ${prData.additions} additions and ${prData.deletions} deletions.`;
  }
}
