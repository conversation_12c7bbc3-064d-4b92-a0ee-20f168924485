import { generateObject } from 'ai';
import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { google } from '@ai-sdk/google';
import { z } from 'zod';
import { PRWalkthroughResult, PullRequestData } from '../types';

// Zod schemas for PR walkthrough analysis
const UseCaseSchema = z.object({
  type: z.enum(['bug_fix', 'new_feature', 'refactor', 'maintenance', 'unclear']),
  description: z.string(),
  concreteExample: z.string().optional(),
  beforeAfter: z.object({
    before: z.string(),
    after: z.string(),
  }).optional(),
});

const ChangeGroupSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  files: z.array(z.string()),
  changeType: z.enum(['core_logic', 'configuration', 'tests', 'documentation', 'mechanical', 'setup']),
  importance: z.enum(['critical', 'important', 'supporting', 'minor']),
  diffHunks: z.array(z.string()).optional(),
  explanation: z.string(),
  reviewNotes: z.array(z.string()).optional(),
});

const PRWalkthroughSchema = z.object({
  useCase: UseCaseSchema,
  summary: z.string(),
  changeGroups: z.array(ChangeGroupSchema),
  reviewOrder: z.array(z.string()), // Array of change group IDs in recommended review order
});

/**
 * Get the AI model based on environment configuration
 * Priority order: OpenAI -> Anthropic -> Google
 */
function getAIModel() {
  if (process.env.OPENAI_API_KEY) {
    console.log('Using OpenAI GPT-4o for PR analysis');
    return openai('gpt-4o');
  } else if (process.env.ANTHROPIC_API_KEY) {
    console.log('Using Anthropic Claude 3.5 Sonnet for PR analysis');
    return anthropic('claude-3-5-sonnet-20241022');
  } else if (process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    console.log('Using Google Gemini 2.5 Flash for PR analysis');
    return google('gemini-2.5-flash-preview-04-17');
  } else {
    throw new Error('No AI provider configured. Please set OPENAI_API_KEY, ANTHROPIC_API_KEY, or GOOGLE_GENERATIVE_AI_API_KEY');
  }
}

/**
 * Generate PR walkthrough analysis using AI
 */
export async function analyzePullRequest(
  prData: PullRequestData,
  changesText: string
): Promise<PRWalkthroughResult> {
  const model = getAIModel();

  const systemPrompt = `You are an expert code reviewer and software engineer. Your goal is to create a walkthrough that makes it easier for developers to understand a PR step by step.

Follow this workflow:

1. IDENTIFY THE USE CASE:
   - If it's a bug fix: provide a concrete example of what didn't work before and works after
   - If it's a new feature: describe the use case for this feature
   - If unclear: call this out explicitly

2. BREAK DOWN AND GROUP CHANGES:
   - Group related changes that make sense to understand together
   - Sometimes group by file, sometimes by concept, sometimes by diff hunk
   - Look for mechanical/similar changes across multiple files
   - Order groups to build understanding incrementally

3. PROVIDE CLEAR EXPLANATIONS:
   - Explain the "why" behind each change group
   - Make it easy to review piece by piece
   - Focus on helping developers understand the logic and flow

Be specific, practical, and focus on making the PR easy to understand and review.`;

  const userPrompt = `Please create a walkthrough for this pull request:

PR Title: ${prData.title}
PR Description: ${prData.body || 'No description provided'}

Changes:
${changesText}

Create a structured walkthrough that:

1. **IDENTIFIES THE USE CASE** (bug fix, feature, etc.) with concrete examples:
   - Explain the use case - what is a concrete example of a problem for our users that this helps with?
   - Focus on the USER PROBLEM, not the technical solution
   - Make sure to talk about the use case and not about the solution in the use case section

2. **BREAKS DOWN CHANGES** into logical groups ordered for incremental understanding:
   - Group related changes that make sense to understand together
   - Order groups to build understanding step by step

3. **EXPLAINS EACH GROUP** clearly to help with review:
   - Why these changes were made
   - How they work together
   - What reviewers should focus on

Focus on making this PR easy to understand and review step by step.`;

  try {
    const result = await generateObject({
      model,
      system: systemPrompt,
      prompt: userPrompt,
      schema: PRWalkthroughSchema,
      temperature: 0.3, // Lower temperature for more consistent analysis
    });

    return {
      ...result.object,
      analysisTimestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Error in AI analysis:', error);
    throw new Error(`AI analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate a quick summary for immediate feedback
 */
export async function generateQuickSummary(prData: PullRequestData): Promise<string> {
  const model = getAIModel();
  
  const prompt = `Provide a brief 2-3 sentence summary of this pull request:

Title: ${prData.title}
Description: ${prData.body || 'No description provided'}
Files changed: ${prData.changed_files}
Additions: ${prData.additions}
Deletions: ${prData.deletions}

Focus on what the PR accomplishes and its scope.`;

  try {
    const result = await generateObject({
      model,
      prompt,
      schema: z.object({
        summary: z.string(),
      }),
    });

    return result.object.summary;
  } catch (error) {
    console.error('Error generating quick summary:', error);
    return `This PR modifies ${prData.changed_files} files with ${prData.additions} additions and ${prData.deletions} deletions.`;
  }
}
