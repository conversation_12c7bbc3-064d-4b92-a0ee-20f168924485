# PR Walkthrough System Setup

This document explains how to set up and use the AI-powered PR walkthrough system in your Next.js application.

## Features

- **Server-side AI Analysis**: All AI processing happens on the server to protect prompts and API keys
- **GitHub Integration**: Fetches PR data and diffs using GitHub's REST API
- **Step-by-step Walkthrough**: Creates an easy-to-follow explanation of PR changes
- **Use Case Identification**: Identifies whether it's a bug fix, feature, etc. with concrete examples
- **Logical Change Grouping**: Groups related changes for incremental understanding
- **Real-time Progress**: Shows analysis progress with loading states
- **Structured Results**: Displays results in organized, user-friendly components

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure your AI provider:

```bash
cp .env.example .env.local
```

Choose one AI provider and set the corresponding API key:

**For OpenAI (GPT-4):**
```
OPENAI_API_KEY=your_openai_api_key_here
```

**For Anthropic (Claude):**
```
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

**Optional - GitHub Token (for private repos or higher rate limits):**
```
GITHUB_TOKEN=your_github_token_here
```

### 2. Install Dependencies

The required packages are already installed:
- `ai` - Vercel AI SDK
- `@ai-sdk/openai` - OpenAI provider
- `@ai-sdk/anthropic` - Anthropic provider
- `@octokit/rest` - GitHub API client
- `zod` - Schema validation

### 3. Usage

Navigate to any GitHub PR URL in your app:
```
/gh/{org}/{repo}/{pr_number}
```

Example:
```
/gh/vercel/next.js/12345
```

Click the "Start Walkthrough" button to begin the AI-powered walkthrough creation.

## API Endpoints

### POST /api/analyze-pr

Analyzes a GitHub PR using AI.

**Request Body:**
```json
{
  "org": "vercel",
  "repo": "next.js", 
  "pr_num": "12345"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "useCase": {
      "type": "bug_fix",
      "description": "...",
      "concreteExample": "...",
      "beforeAfter": { "before": "...", "after": "..." }
    },
    "summary": "...",
    "changeGroups": [
      {
        "id": "group1",
        "title": "Core Logic Changes",
        "description": "...",
        "files": ["file1.ts", "file2.ts"],
        "changeType": "core_logic",
        "importance": "critical",
        "explanation": "...",
        "reviewNotes": ["..."]
      }
    ],
    "reviewOrder": ["group1", "group2"],
    "overallAssessment": {
      "complexity": "medium",
      "riskLevel": "low",
      "readinessScore": 8,
      "keyReviewPoints": ["..."]
    },
    "analysisTimestamp": "2024-01-01T00:00:00.000Z"
  },
  "prData": { ... }
}
```

## Walkthrough Structure

The system creates a structured walkthrough with these components:

### 1. **Use Case Identification**
   - **Type**: bug_fix, new_feature, refactor, maintenance, unclear
   - **Description**: Clear explanation of what the PR accomplishes
   - **Concrete Example**: Specific scenario that demonstrates the change
   - **Before/After**: What didn't work vs. what works now (for bug fixes)

### 2. **Change Groups**
   Each group contains:
   - **Title & Description**: What this group of changes does
   - **Files**: Which files are affected
   - **Change Type**: core_logic, configuration, tests, documentation, mechanical, setup
   - **Importance**: critical, important, supporting, minor
   - **Explanation**: Why these changes were made
   - **Review Notes**: Specific things to pay attention to

### 3. **Review Order**
   - Groups ordered for incremental understanding
   - Start with core logic, then supporting changes
   - Build understanding step by step

### 4. **Overall Assessment**
   - **Complexity**: How complex is this PR to understand
   - **Risk Level**: How risky are these changes
   - **Readiness Score**: How ready is this PR for merge (1-10)
   - **Key Review Points**: Most important things to focus on

## Components

### Core Components

- `PRWalkthrough` - Main walkthrough component with API integration
- `AnalysisCard` - Reusable card for walkthrough sections
- `ProgressBar` - Shows analysis progress with stage indicators
- `IssuesList` - Displays lists with appropriate icons and colors (used in legacy component)

### Services

- `aiAnalysis.ts` - AI analysis service with structured prompts
- `githubUtils.ts` - GitHub API utilities for fetching PR data

## Customization

### Adding New Change Types or Assessment Criteria

1. Update types in `src/types/index.ts` (ChangeGroup interface)
2. Add new schema validation in `src/services/aiAnalysis.ts`
3. Update the walkthrough prompt to include new criteria
4. Add UI components for displaying the new information

### Changing AI Providers

The system automatically detects which AI provider to use based on environment variables. You can modify the `getAIModel()` function in `aiAnalysis.ts` to add support for additional providers.

### Caching Results

Currently, analysis results are not cached. You can implement caching by:
1. Adding a database or Redis
2. Storing results with PR URL as key
3. Implementing cache invalidation logic

## Troubleshooting

### Common Issues

1. **"No AI provider configured"** - Set either `OPENAI_API_KEY` or `ANTHROPIC_API_KEY`
2. **GitHub API rate limits** - Add `GITHUB_TOKEN` for higher limits
3. **Large PRs timing out** - Consider implementing streaming responses for large analyses

### Rate Limits

- GitHub API: 60 requests/hour (unauthenticated), 5000/hour (authenticated)
- OpenAI API: Varies by plan
- Anthropic API: Varies by plan

## Security Considerations

- All AI prompts and analysis logic run server-side
- API keys are never exposed to the client
- GitHub tokens should have minimal required permissions
- Consider implementing rate limiting for the analysis endpoint
