# PR Analysis System Setup

This document explains how to set up and use the AI-powered PR analysis system in your Next.js application.

## Features

- **Server-side AI Analysis**: All AI processing happens on the server to protect prompts and API keys
- **GitHub Integration**: Fetches PR data and diffs using GitHub's REST API
- **Comprehensive Analysis**: Evaluates code quality, security, performance, testing, and documentation
- **Real-time Progress**: Shows analysis progress with loading states
- **Structured Results**: Displays results in organized, user-friendly components

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure your AI provider:

```bash
cp .env.example .env.local
```

Choose one AI provider and set the corresponding API key:

**For OpenAI (GPT-4):**
```
OPENAI_API_KEY=your_openai_api_key_here
```

**For Anthropic (Claude):**
```
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

**Optional - GitHub Token (for private repos or higher rate limits):**
```
GITHUB_TOKEN=your_github_token_here
```

### 2. Install Dependencies

The required packages are already installed:
- `ai` - Vercel AI SDK
- `@ai-sdk/openai` - OpenAI provider
- `@ai-sdk/anthropic` - Anthropic provider
- `@octokit/rest` - GitHub API client
- `zod` - Schema validation

### 3. Usage

Navigate to any GitHub PR URL in your app:
```
/gh/{org}/{repo}/{pr_number}
```

Example:
```
/gh/vercel/next.js/12345
```

Click the "Start Analysis" button to begin the AI analysis process.

## API Endpoints

### POST /api/analyze-pr

Analyzes a GitHub PR using AI.

**Request Body:**
```json
{
  "org": "vercel",
  "repo": "next.js", 
  "pr_num": "12345"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": "...",
    "overallScore": 8,
    "codeQuality": { ... },
    "security": { ... },
    "performance": { ... },
    "testing": { ... },
    "documentation": { ... },
    "keyChanges": [...],
    "recommendations": [...],
    "analysisTimestamp": "2024-01-01T00:00:00.000Z"
  },
  "prData": { ... }
}
```

## Analysis Categories

The system analyzes PRs across five key areas:

1. **Code Quality** (1-10 score)
   - Code complexity assessment
   - Maintainability issues
   - Best practice adherence

2. **Security** (risk level: low/medium/high/critical)
   - Vulnerability detection
   - Security best practices
   - Risk assessment

3. **Performance** (impact: positive/negative/neutral)
   - Performance implications
   - Optimization opportunities
   - Resource usage concerns

4. **Testing** (coverage: good/partial/missing)
   - Test coverage analysis
   - Test quality assessment
   - Missing test identification

5. **Documentation** (quality: excellent/good/fair/poor)
   - Documentation completeness
   - Code comments quality
   - README updates

## Components

### Core Components

- `PRAnalysis` - Main analysis component with API integration
- `AnalysisCard` - Reusable card for analysis sections
- `IssuesList` - Displays issues with appropriate icons and colors
- `ProgressBar` - Shows analysis progress with stage indicators

### Services

- `aiAnalysis.ts` - AI analysis service with structured prompts
- `githubUtils.ts` - GitHub API utilities for fetching PR data

## Customization

### Adding New Analysis Categories

1. Update types in `src/types/index.ts`
2. Add new schema in `src/services/aiAnalysis.ts`
3. Update the analysis prompt
4. Add UI components for the new category

### Changing AI Providers

The system automatically detects which AI provider to use based on environment variables. You can modify the `getAIModel()` function in `aiAnalysis.ts` to add support for additional providers.

### Caching Results

Currently, analysis results are not cached. You can implement caching by:
1. Adding a database or Redis
2. Storing results with PR URL as key
3. Implementing cache invalidation logic

## Troubleshooting

### Common Issues

1. **"No AI provider configured"** - Set either `OPENAI_API_KEY` or `ANTHROPIC_API_KEY`
2. **GitHub API rate limits** - Add `GITHUB_TOKEN` for higher limits
3. **Large PRs timing out** - Consider implementing streaming responses for large analyses

### Rate Limits

- GitHub API: 60 requests/hour (unauthenticated), 5000/hour (authenticated)
- OpenAI API: Varies by plan
- Anthropic API: Varies by plan

## Security Considerations

- All AI prompts and analysis logic run server-side
- API keys are never exposed to the client
- GitHub tokens should have minimal required permissions
- Consider implementing rate limiting for the analysis endpoint
